package com.tfc.model;

import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.awt.geom.Point2D;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Demonstration test showing how element IDs work for debugging purposes.
 * This test shows how each element type gets a unique ID that can be used
 * to identify elements in debugging output.
 */
class ElementIdDemonstrationTest {

    @BeforeEach
    void setUp() {
        // Reset counter to ensure predictable IDs for demonstration
        ElementIdGenerator.resetCounter();
    }

    @Test
    void demonstrateElementIdGeneration() {
        // Create various PDF elements and show their IDs
        
        // Text elements
        PDFTextElement text1 = new PDFTextElement(
                "Hello World",
                new Point2D.Float(10.0f, 20.0f),
                12.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );
        
        PDFTextElement text2 = new PDFTextElement(
                "Another text",
                new Point2D.Float(30.0f, 40.0f),
                14.0f,
                new PDType1Font(Standard14Fonts.FontName.TIMES_ROMAN),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );

        // Shape elements
        PDFShapeElement shape1 = new PDFShapeElement(
                50.0f, 60.0f, 100.0f, 80.0f,
                new RGBA(255, 0, 0, 255),
                new RGBA(0, 255, 0, 255)
        );
        
        PDFShapeElement shape2 = new PDFShapeElement(
                150.0f, 160.0f, 200.0f, 180.0f,
                new RGBA(0, 0, 255, 255),
                null
        );

        // Line elements
        PDFLineElement line1 = new PDFLineElement(
                0.0f, 0.0f, 100.0f, 100.0f,
                new RGBA(128, 128, 128, 255)
        );
        
        PDFLineElement line2 = new PDFLineElement(
                200.0f, 200.0f, 300.0f, 250.0f,
                new RGBA(64, 64, 64, 255)
        );

        // Image elements
        Image image1 = new Image();
        image1.size = new Size();
        image1.size.width = 100.0f;
        image1.size.height = 150.0f;
        
        Image image2 = new Image();
        image2.size = new Size();
        image2.size.width = 200.0f;
        image2.size.height = 250.0f;

        // Verify IDs are unique and follow expected format
        assertEquals("TEXT_000001", text1.getId());
        assertEquals("TEXT_000002", text2.getId());
        assertEquals("SHAPE_000003", shape1.id());
        assertEquals("SHAPE_000004", shape2.id());
        assertEquals("LINE_000005", line1.id());
        assertEquals("LINE_000006", line2.id());
        assertEquals("IMAGE_000007", image1.getId());
        assertEquals("IMAGE_000008", image2.getId());

        // Demonstrate toString output includes IDs
        logger.debug("=== Element IDs in toString output ===");
        logger.debug(text1.toString());
        logger.debug(text2.toString());
        logger.debug(shape1.toString());
        logger.debug(shape2.toString());
        logger.debug(line1.toString());
        logger.debug(line2.toString());
        logger.debug(image1.toString());
        logger.debug(image2.toString());

        // Verify all IDs are unique
        String[] allIds = {
                text1.getId(), text2.getId(),
                shape1.id(), shape2.id(),
                line1.id(), line2.id(),
                image1.getId(), image2.getId()
        };

        for (int i = 0; i < allIds.length; i++) {
            for (int j = i + 1; j < allIds.length; j++) {
                assertNotEquals(allIds[i], allIds[j], 
                        "All element IDs should be unique: " + allIds[i] + " vs " + allIds[j]);
            }
        }
    }

    @Test
    void demonstrateIdPersistenceInPDFPageStructure() {
        // Reset counter for predictable IDs
        ElementIdGenerator.resetCounter();
        
        PDFPageStructure pageStructure = new PDFPageStructure();

        // Add elements to page structure
        PDFTextElement textElement = new PDFTextElement(
                "Debug Text",
                new Point2D.Float(0.0f, 0.0f),
                10.0f,
                new PDType1Font(Standard14Fonts.FontName.HELVETICA),
                new double[]{1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
                1
        );
        pageStructure.addTextElement(textElement);

        PDFShapeElement shapeElement = new PDFShapeElement(
                10.0f, 20.0f, 30.0f, 40.0f,
                new RGBA(255, 0, 0, 255),
                null
        );
        pageStructure.addShapeElement(shapeElement);

        PDFLineElement lineElement = new PDFLineElement(
                0.0f, 0.0f, 50.0f, 50.0f,
                new RGBA(0, 255, 0, 255)
        );
        pageStructure.addLineElement(lineElement);

        Image imageElement = new Image();
        imageElement.size = new Size();
        imageElement.size.width = 100.0f;
        imageElement.size.height = 200.0f;
        pageStructure.addImageElement(imageElement);

        // Verify IDs are preserved in the page structure
        assertEquals("TEXT_000001", textElement.getId());
        assertEquals("SHAPE_000002", shapeElement.id());
        assertEquals("LINE_000003", lineElement.id());
        assertEquals("IMAGE_000004", imageElement.getId());

        // Show pretty output includes IDs
        logger.debug("=== PDFPageStructure pretty output with IDs ===");
        logger.debug(pageStructure.pretty());

        // Verify elements can be retrieved and still have their IDs
        assertEquals(textElement.getId(), pageStructure.getElements().get(0).getId());
        assertEquals(shapeElement.id(), pageStructure.getShapes().get(0).id());
        assertEquals(lineElement.id(), pageStructure.getLines().get(0).id());
        assertEquals(imageElement.getId(), pageStructure.getImages().get(0).getId());
    }
}
